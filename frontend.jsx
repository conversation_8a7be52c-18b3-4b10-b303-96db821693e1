import React, { useState } from 'react';

// Main App component
const App = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [results, setResults] = useState([]);
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');

    // Function to handle the search and download process
    const handleSearch = async () => {
        if (!searchQuery.trim()) {
            setMessage('Please enter a search query.');
            return;
        }

        setLoading(true);
        setResults([]);
        setMessage('');

        try {
            // Make a fetch request to the backend (simulated by tool_code)
            const response = await fetch('/search_and_download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: searchQuery }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.error) {
                setMessage(`Error: ${data.error}`);
                setLoading(false);
                return;
            }

            if (data.downloadedPages && data.downloadedPages.length > 0) {
                setResults(data.downloadedPages);
                setMessage(`Successfully fetched ${data.downloadedPages.length} pages.`);
            } else {
                setMessage('No organic search results found or able to be downloaded.');
            }

        } catch (error) {
            console.error('Error during search and download:', error);
            setMessage(`Failed to search and download: ${error.message}. Please try again.`);
        } finally {
            setLoading(false);
        }
    };

    // Function to download a single HTML page
    const downloadHtml = (htmlContent, title) => {
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4 font-inter">
            <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-2xl">
                <h1 className="text-3xl font-bold text-center text-gray-800 mb-6">
                    HTML Page Downloader
                </h1>

                <div className="mb-6">
                    <label htmlFor="search-query" className="block text-gray-700 text-sm font-medium mb-2">
                        Enter Search Query:
                    </label>
                    <input
                        type="text"
                        id="search-query"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="e.g., top 10 e-commerce sites"
                        onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                                handleSearch();
                            }
                        }}
                    />
                </div>

                <button
                    onClick={handleSearch}
                    className="w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={loading}
                >
                    {loading ? (
                        <svg className="animate-spin h-5 w-5 text-white mr-3" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.0 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    ) : (
                        'Search and Download'
                    )}
                </button>

                {message && (
                    <p className="mt-4 text-center text-sm font-medium text-gray-600">
                        {message}
                    </p>
                )}

                {results.length > 0 && (
                    <div className="mt-8 border-t border-gray-200 pt-6">
                        <h2 className="text-xl font-semibold text-gray-800 mb-4">Downloaded Pages:</h2>
                        <ul className="space-y-4">
                            {results.map((page, index) => (
                                <li key={index} className="bg-gray-50 p-4 rounded-md shadow-sm border border-gray-200">
                                    <h3 className="text-lg font-medium text-blue-700 break-words">
                                        <a href={page.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                            {page.title || 'No Title'}
                                        </a>
                                    </h3>
                                    <p className="text-gray-600 text-sm mt-1 break-words">{page.url}</p>
                                    <div className="mt-3 flex space-x-2">
                                        <button
                                            onClick={() => downloadHtml(page.htmlContent, page.title || `page_${index}`)}
                                            className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition duration-200"
                                        >
                                            Download HTML
                                        </button>
                                        <button
                                            onClick={() => {
                                                const newWindow = window.open();
                                                newWindow.document.write(page.htmlContent);
                                                newWindow.document.close();
                                            }}
                                            className="px-4 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700 transition duration-200"
                                        >
                                            View HTML
                                        </button>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
};

export default App;
