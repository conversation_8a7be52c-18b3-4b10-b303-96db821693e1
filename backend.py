import json
from google_search import search
from browsing import browse

# This function will be called by the frontend request
def search_and_download_html(query):
    downloaded_pages = []
    try:
        # Perform a Google search
        search_results = search(queries=[query])

        if not search_results or not search_results[0].results:
            return {"downloadedPages": [], "error": "No search results found."}

        # Filter out potential sponsored links (google_search usually returns organic, but as a safeguard)
        # The PerQueryResult structure doesn't explicitly mark sponsored links,
        # so we'll assume the search tool primarily returns organic results.
        organic_results = [
            r for r in search_results[0].results
            if r.url and not ("adurl" in r.url or "googleadservices" in r.url) # Basic URL check
        ]

        # Limit to a reasonable number of results to avoid excessive browsing
        # For example, let's process up to the first 10 organic results.
        results_to_process = organic_results[:10]

        for result in results_to_process:
            url = result.url
            title = result.source_title or result.snippet # Use source_title or snippet as title
            if url:
                try:
                    # Browse the URL to get the HTML content
                    html_content = browse(query=url, url=url)
                    downloaded_pages.append({
                        "url": url,
                        "title": title,
                        "htmlContent": html_content
                    })
                except Exception as e:
                    print(f"Error browsing {url}: {e}")
                    # Continue to next URL even if one fails
    except Exception as e:
        print(f"Error during search or initial processing: {e}")
        return {"downloadedPages": [], "error": str(e)}

    return {"downloadedPages": downloaded_pages}

# This block handles the incoming request from the frontend
if __name__ == "__main__":
    # Simulate receiving a POST request body
    # In a real environment, this would come from a web server
    import sys
    input_data = json.loads(sys.stdin.read())
    query = input_data.get("query")

    if query:
        response_data = search_and_download_html(query)
        print(json.dumps(response_data))
    else:
        print(json.dumps({"error": "No search query provided."}))
